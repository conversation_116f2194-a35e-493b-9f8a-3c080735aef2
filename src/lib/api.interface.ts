// Coin Details API response interface

// Link type for social/website link

interface CoinLink {
  name: string;
  url: string;
  type: string;
}

// Supply information
interface Supply {
  confirmed: boolean;
  supplyAt: number;
  max: number | null;
  total: string;
  circulating: string;
}

// All-time high price information
interface AllTimeHigh {
  price: string;
  timestamp: number;
}

// Coin details
export interface Coin {
  index?: number;
  isBookmarked?: boolean;
  uuid: string;
  symbol: string;
  name: string;
  description: string;
  color: string;
  iconUrl: string;
  websiteUrl: string;
  links: CoinLink[];
  supply: Supply;
  numberOfMarkets: number;
  numberOfExchanges: number;
  "24hVolume": string;
  marketCap: string;
  fullyDilutedMarketCap: string;
  price: string;
  btcPrice: string;
  priceAt: number;
  change_1h: string;
  change_24h: string;
  change_7d: string;
  rank: number;
  sparkline: (string | null)[];
  allTimeHigh: AllTimeHigh;
  coinrankingUrl: string;
  tier: number;
  lowVolume: boolean;
  listedAt: number;
  hasContent: boolean;
  notices: null | string;
  contractAddresses: string[];
  tags: string[];
  dominance: string;
  change: string;
}

// Nested data structure
interface CoinData {
  coin: Coin;
}

// API response wrapper
export interface CoinDetailsApiResponse {
  data: {
    status: string;
    data: CoinData;
  };
  message: string;
  error: string;
}

// Coin Price History API response interface
export type CoinPriceHistoryApiResponse = {
  data: {
    status: "success" | "failure"; // Assuming "failure" could also be a possible value
    data: {
      change: string; // Assuming the change value is always a string
      history: Array<{
        price: string; // Price as a string
        timestamp: number; // Timestamp as a number
      }>;
    };
  };
  message: string; // Message can be "success" or other messages
  error: string; // Error message, empty string if no error
};

// Blockchains API response interface
export interface BlockchainsApiResponse {
  data: BlockchainData;
  message: string;
  error: string;
}

interface BlockchainData {
  status: string;
  data: Blockchains;
}

interface Blockchains {
  blockchains: string[];
}

// Bookmarks API response interface

export interface BookmarksApiResponse {
  data: Bookmarks;
  message: string;
  error: string;
}

export interface Bookmarks {
  totalDocuments: number;
  bookmarks: Bookmark[];
}

export interface Bookmark {
  _id: string;
  title: string;
  coinIds: string[];
  userId: string;
  __v: number;
}

// Coin Market Scenarios API response interface

export interface CoinMarketScenariosApiResponse {
  data: Data;
  message: string;
  error: string;
}

export interface Data {
  topGainers: Coin[];
  trendingAssets: Coin[];
  topLosers: Coin[];
}

// GetAllCoins API response interface
export interface GetAllCoinsApiResponse {
  data: GetAllCoins;
  message: string;
  error: string;
}

export interface GetAllCoins {
  data: GetAllCoins2;
}

export interface GetAllCoins2 {
  coins: Coin[];
  stats: Stats;
}

export interface Stats {
  total: number;
  totalCoins: number;
  totalMarkets: number;
  totalExchanges: number;
  totalMarketCap: string;
  total24hVolume: string;
}

// Coin Stats API response interface

export interface CoinStatsApiResponse {
  data: CoinStats;
  message: string;
  error: string;
}

export interface CoinStats {
  status: string;
  data: CoinStatsData;
}

export interface CoinStatsData {
  referenceCurrencyRate: number;
  totalCoins: number;
  totalMarkets: number;
  totalExchanges: number;
  totalMarketCap: string;
  total24hVolume: string;
  btcDominance: number;
  bestCoins: BestCoin[];
  newestCoins: NewestCoin[];
}

export interface BestCoin {
  uuid: string;
  symbol: string;
  name: string;
  iconUrl: string;
  coinrankingUrl: string;
}

export interface NewestCoin {
  uuid: string;
  symbol: string;
  name: string;
  iconUrl: string;
  coinrankingUrl: string;
}

// learn

export interface LearnByCoidIDResponse {
  data: Daum[];
  message: string;
  error: string;
}

export interface Daum {
  _id: string;
  title: string;
  content: Content;
  category: string;
  level: string;
  refrencedLinks: any[];
  coinIds: string[];
  createdAt: string;
  updatedAt: string;
  __v: number;
}

export interface Content {
  headers: string;
  headersDescription: string;
  contentLearnings: string[];
  introduction: string;
  about: string;
  working: string;
  links: any[];
  timeTook: number;
}

export interface Seo {
  title: string;
  description: string;
  keywords: string;
}

//
export interface AffiliateLinksByCoidIDResponse {
  data: DaumAff[];
  message: string;
  error: string;
}

export interface DaumAff {
  _id: string;
  title: string;
  description: string;
  imageUrl: string;
  affliateLinks: string;
  coinId: string;
  createdAt: string;
  updatedAt: string;
  __v: number;
}

// Bookmark by Id response

export interface BookmarkByIdResponse {
  data: BookmarkById;
  message: string;
  error: string;
}

export interface BookmarkById {
  title: string;
  coins: [Coin[], CoinsBookmarked];
}

export interface CoinsBookmarked {
  total: number;
  totalCoins: number;
  totalMarkets: number;
  totalExchanges: number;
  totalMarketCap: string;
  total24hVolume: string;
}

// projects interface

export interface ProjectsApiResponse {
  data: ProjectData;
  message: string;
  error: string;
}

export interface ProjectData {
  totalDocuments: number;
  projects: Project[];
}

export interface Project {
  _id: string;
  title: string;
  projectIconUrl: string;
  description: string;
  content: Content;
  slug: string;
  tags: string[];
  seo: Seo;
  blockchainName: string;
  blockchainIconUrl: string;
  createdAt: string;
  updatedAt: string;
  __v: number;
}

export interface Content {
  headers: string;
  headersDescription: string;
  introduction: string;
  about: string;
  working: string;
  overview: string;
  links: any[];
}

export interface CategoriesApiResponse {
  data: CategoriesData;
  message: string;
  error: string;
}

export interface CategoriesData {
  totalDocuments: number;
  learnCategories: LearnCategory[];
}

export interface LearnCategory {
  _id: string;
  title: string;
  description: string;
  quotes: string;
  quotesWrittenBy: string;
  createdAt: string;
  updatedAt: string;
  imageUrl: string;
  __v: number;
}

export interface LearnBlogsApiResponse {
  data: LearnBlogData;
  message: string;
  error: string;
}

export interface LearnBlogData {
  totalDocuments: number;
  learns: Learn[];
}

export interface Learn {
  coinIds: string[];
  _id: string;
  slug: string;
  title: string;
  content: Content;
  seo: Seo;
  category: string;
  level: string;
  refrencedLinks: any[];
  createdAt: string;
  updatedAt: string;
  __v: number;
  thumbnailUrl: string;
}

export interface Content {
  headers: string;
  headersDescription: string;
  contentLearnings: string[];
  introduction: string;
  about: string;
  working: string;
  links: any[];
  body?: string;
  timeTook: number;
}

export interface LearnByIdApiResponse {
  data: Learn;
  message: string;
  error: string;
}

export interface LearnCategoryByIdApiResponse {
  data: LearnCategory;
  message: string;
  error: string;
}

export interface ProjectByIdApiResponse {
  data: Project;
  message: string;
  error: string;
}

export interface EmailVerificationResponse {
  data: unknown;
  message: string;
  error: string;
}

export interface ResetPasswordResponse {
  data: unknown;
  message: string;
  error: string;
}

export interface RelatedProjectApiResponse {
  data: RelatedProjectData;
  message: string;
  error: string;
}

export interface RelatedProjectData {
  totalDocuments: number;
  relatedProjects: Project[];
}

export interface OhlcDataApiResponse {
  data: OhlcData;
  message: string;
  error: string;
}

export interface OhlcData {
  status: string;
  data: OhlcData2;
}

export interface OhlcData2 {
  ohlc: Ohlc[];
}

export interface Ohlc {
  startingAt: number;
  endingAt: number;
  open?: string;
  high?: string;
  low?: string;
  close?: string;
  avg?: string;
  "24hVolume"?: string;
  intervalVolume?: string;
}

export interface CoinsByCategoriesApiResponse {
  data: CoinsByCategoriesData;
  message: string;
  error: string;
}

export interface CoinsByCategoriesData {
  title: string;
  data: CoinsByCategoriesData2;
}

export interface CoinsByCategoriesData2 {
  coins: Coin[];
  stats: Stats;
}

export interface ProfileApiResponse {
  data: User;
  message: string;
  error: string;
}

export interface User {
  _id: string;
  username: string;
  user_id: string;
  email: string;
  role: string;
  status: string;
  createdAt: string;
  profilePicUrl: string;
  updatedAt: string;
  __v: number;
}

export interface PostApiResponse {
  data: string;
  message: string;
  error: string;
}

export interface ResearchApiResponse {
  data: ResearchData;
  message: string;
  error: string;
}

export interface ResearchData {
  totalDocuments: number;
  researches: Learn[];
}

export interface ResearchByIdApiResponse {
  data: Learn;
  message: string;
  error: string;
}

export interface ResearchCategoryApiResponse {
  data: ResearchCategoryData;
  message: string;
  error: string;
}

export interface ResearchCategoryData {
  totalDocuments: number;
  researchCategories: ResearchCategory[];
}

export interface ResearchCategory {
  _id: string;
  title: string;
  description: string;
  createdAt: string;
  updatedAt: string;
  __v: number;
}

export interface BitcoinDominanceApiResponse {
  data: BitcoinDominanceData[];
  message: string;
  error: string;
}

export interface BitcoinDominanceData {
  timestamp: number;
  price: string;
  dominance: number;
}

export interface FinanceHistoryApiResponse {
  data: FinanceHistoryData;
  message: string;
  error: string;
}

export interface FinanceHistoryData {
  sp500: Sp500[];
  gold: Gold[];
  bitcoin: Bitcoin[];
}

export interface Sp500 {
  date: string;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
  adjClose: number;
}

export interface Gold {
  date: string;
  open?: number;
  high?: number;
  low?: number;
  close?: number;
  volume: number;
  adjClose?: number;
}

export interface Bitcoin {
  date: string;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
  adjClose: number;
}

export interface ExchangeStatsApiResponse {
  data: ExchangeStatsData;
  message: string;
  error: string;
}

export interface CBDCCurrency {
  uid: string;
  digitalCurrency: string;
  country: string;
  centralBank: string;
  announcementYear: number;
  status: "Pilot" | "Proof of concept" | "Research" | "Launched" | "Cancelled";
  tag?: string;
}

export interface CBDCData {
  message: string;
  error: string;
  data: CBDCCurrency[];
}

interface Tag {
  name: string;
  currency: string;
}

export interface TimelineChange {
  property: string;
  valueOld: string | null;
  valueNew: string | null;
}

interface TagData {
  tag: Tag;
  description: string;
  changes: TimelineChange[];
}

interface TimelineEntry {
  year: number;
  month: number;
  description: string | null;
  tags: TagData[];
}
export interface TimelineData {
  last: boolean;
  first: boolean;
  totalElements: number;
  totalPages: number;
  size: number;
  number: number;
  sort: null;
  numberOfElements: number;
  content: TimelineEntry[];
}
export interface CBDCTimelineData {
  message: string;
  error: string;
  data: TimelineData;
}

export interface ExchangeStatsData {
  status: string;
  data: ExchangeStatsData2;
}

export interface ExchangeStatsData2 {
  stats: ExchangeStats;
  exchanges: Exchange[];
}

export interface ExchangeStats {
  "24hVolume": string;
  total: number;
}

export interface Exchange {
  uuid: string;
  rank: number;
  name: string;
  iconUrl: string;
  verified: boolean;
  recommended: boolean;
  numberOfMarkets?: number;
  numberOfCoins?: number;
  marketShare: string;
  coinrankingUrl: string;
  "24hVolume"?: string;
}

export interface BitcoinAlertsApiResponse {
  data: BitcoinAlertsData;
  message: string;
  error: string;
}

export interface BitcoinAlertsData {
  totalDocuments: number;
  bitcoinAlerts: BitcoinAlert[];
}

export interface BitcoinAlert {
  _id: string;
  hash: string;
  to: string[];
  from: string[];
  amount: string;
  amountInUsd: string;
  timestamp: string;
  level: string;
  assetType: string;
  createdAt: string;
  updatedAt: string;
  __v: number;
}

export interface FilterAlert {
  alertName: string;
  minimumTransferValue: string;
  selectedCoins: string[];
}

export interface BitcoinNumberOfTransactionsApiResponse {
  data: BitcoinNumberOfTransactionsData[];
  message: string;
  error: string;
}

export interface MofseAdvanceChartsApiResponse {
  data: BitcoinNumberOfTransactionsData[];
  message: string;
  error: string;
}

export interface BitcoinNumberOfTransactionsData {
  date: string;
  count: number;
}

export interface EthereumNumberOfAddressApiResponse {
  data: EthereumNumberOfAddress[];
  message: string;
  error: string;
}

export interface EthereumNumberOfAddress {
  date: string;
  senderCount: number;
  recieverCount: number;
  totalCount: number;
}

// FRED API interfaces
export interface FREDObservation {
  realtime_start: string;
  realtime_end: string;
  date: string;
  value: string;
}

export interface FREDObservationsResponse {
  realtime_start: string;
  realtime_end: string;
  observation_start: string;
  observation_end: string;
  units: string;
  output_type: number;
  file_type: string;
  order_by: string;
  sort_order: string;
  count: number;
  offset: number;
  limit: number;
  observations: FREDObservation[];
}

export interface FREDSeries {
  id: string;
  realtime_start: string;
  realtime_end: string;
  title: string;
  observation_start: string;
  observation_end: string;
  frequency: string;
  frequency_short: string;
  units: string;
  units_short: string;
  seasonal_adjustment: string;
  seasonal_adjustment_short: string;
  last_updated: string;
  popularity: number;
  notes: string;
}

export interface FREDSeriesResponse {
  realtime_start: string;
  realtime_end: string;
  seriess: FREDSeries[];
}

// Backend FRED API response interfaces (wrapped in your standard format)
export interface FREDObservationsApiResponse {
  data: FREDObservationsResponse;
  message: string;
  error: string;
}

export interface FREDSeriesApiResponse {
  data: FREDSeriesResponse;
  message: string;
  error: string;
}
