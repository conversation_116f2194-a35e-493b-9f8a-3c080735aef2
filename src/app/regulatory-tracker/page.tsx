"use client";
import Footer from "@/components/comman/Footer";
import Header from "@/components/comman/Header";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import React, { useState, useMemo } from "react";
import SearchBar from "@/components/comman/SearchBar"; // Re-using SearchBar component
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"; // Assuming you have shadcn/ui select component

// Data from the provided document, parsed into a structured format
const narrativesData = [
  {
    month: "June 2025",
    entries: [
      {
        country: "United States",
        text: "Senate approves a major stablecoin law, requiring issuers to hold dollar-for-dollar reserves in cash or Treasury bills and report monthly to regulators for transparency and trust.",
      },
      {
        country: "United States",
        text: "House aligns its stablecoin bill with the Senate’s, resolving disputes over state versus federal oversight, with a final law expected by July.",
      },
      {
        country: "United Kingdom",
        text: "Financial regulators push rules for fiat-backed stablecoins used in payments, aiming to finalize by December to protect users and ensure market stability.",
      },
      {
        country: "Hong Kong",
        text: "Completes stablecoin licensing framework, allowing banks to issue tokens under strict reserve and anti-money laundering rules, integrating them into finance systems.",
      },
      {
        country: "Singapore",
        text: "Strengthens crypto-friendly policies, making it easier to use stablecoins in payments, cementing its role as a global leader in digital assets.",
      },
      {
        country: "European Union",
        text: "Enforces strict crypto regulations, removing non-compliant stablecoins from exchanges, with banking authorities ensuring reserve audits to safeguard investors.",
      },
      {
        country: "Global",
        text: "International regulators issue guidelines to address stablecoin risks, pushing for global anti-money laundering standards to prevent illegal transactions.",
      },
      {
        country: "Stablecoin Market",
        text: "Stablecoin market grows beyond $230 billion, mostly dollar-linked, fueling adoption in decentralized finance and cross-border payments.",
      },
    ],
  },
  {
    month: "May 2025",
    entries: [
      {
        country: "United States",
        text: "Senate prepares to vote on stablecoin law, with lawmakers addressing user protections like guaranteed redemption and penalties for reserve mismanagement.",
      },
      {
        country: "United States",
        text: "New securities regulator shifts focus to clear stablecoin rules, easing enforcement to encourage innovation while tackling fraud risks.",
      },
      {
        country: "Hong Kong",
        text: "Passes law allowing licensed firms to issue stablecoins, requiring full fiat backing and regular audits to ensure transparency and compliance.",
      },
      {
        country: "Hong Kong",
        text: "Starts a testing program for stablecoin payments, aiming to integrate them into retail and international transactions by 2026.",
      },
      {
        country: "Global",
        text: "Global anti-money laundering rules tighten, requiring crypto platforms to share transaction details across borders to fight financial crime.",
      },
      {
        country: "Stablecoin Market",
        text: "Banks and fintechs worldwide finalize systems to support stablecoins, driven by clear regulations and rising demand for digital payments.",
      },
      {
        country: "Global",
        text: "Major economies discuss unified stablecoin rules, aiming to reduce financial risks and ensure smooth cross-border payment systems.",
      },
    ],
  },
  {
    month: "April 2025",
    entries: [
      {
        country: "United States",
        text: "Lawmakers debate stablecoin bills, criticized for not regulating foreign issuers, raising concerns about market risks from overseas tokens.",
      },
      {
        country: "United States",
        text: "Commodity regulator suggests treating some stablecoins as commodities, creating overlap with securities oversight, complicating rules for issuers.",
      },
      {
        country: "United Arab Emirates",
        text: "Leads Middle East with a new crypto framework, encouraging stablecoin innovation to make Dubai a digital finance leader.",
      },
      {
        country: "Australia",
        text: "Lags in stablecoin rules, slowing local projects, with regulators planning a 2026 framework to improve competitiveness.",
      },
      {
        country: "European Union",
        text: "Strict crypto rules force exchanges to meet reserve and licensing requirements, removing non-compliant stablecoins to protect users.",
      },
      {
        country: "Global",
        text: "International crime agency warns of stablecoins being used to evade sanctions, calling for stronger monitoring of global transactions.",
      },
      {
        country: "Stablecoin Market",
        text: "Major payment firms adopt stablecoins for transactions, boosted by regulatory progress in Europe and Asia.",
      },
    ],
  },
  {
    month: "March 2025",
    entries: [
      {
        country: "United States",
        text: "Banking regulator allows major banks to hold stablecoin reserves, enabling traditional finance to issue or manage digital currencies.",
      },
      {
        country: "United States",
        text: "House advances stablecoin bill, but critics worry federal rules could weaken state-level consumer protections, prompting amendments.",
      },
      {
        country: "United States",
        text: "Treasury forms group to study stablecoins’ impact on the dollar’s global role, focusing on their use in international trade.",
      },
      {
        country: "Singapore and Hong Kong",
        text: "Test stablecoin use in payment systems, balancing innovation with strict rules to prevent financial crime.",
      },
      {
        country: "United Kingdom",
        text: "Refines rules for payment-focused stablecoins, requiring full fiat backing and planning a broader crypto framework by 2026.",
      },
      {
        country: "Global",
        text: "Banking regulators propose reserve requirements for banks holding stablecoins, aiming to reduce risks to financial stability.",
      },
      {
        country: "Stablecoin Market",
        text: "Stablecoin use in decentralized finance grows, with platforms adopting compliant tokens as regulations clarify globally.",
      },
    ],
  },
  {
    month: "February 2025",
    entries: [
      {
        country: "United States",
        text: "Proposes stablecoin laws requiring full reserves and audits, with oversight shared among banking, securities, and commodity regulators.",
      },
      {
        country: "United States",
        text: "Faces pushback for not addressing foreign stablecoin issuers, with calls for sanctions to prevent regulatory gaps and risks.",
      },
      {
        country: "United States",
        text: "Advances broader crypto bill to define whether tokens are securities or commodities, impacting stablecoin classifications.",
      },
      {
        country: "United States",
        text: "Securities regulator relaunches crypto task force to create clear registration and disclosure rules for digital assets.",
      },
      {
        country: "Stablecoin Market",
        text: "Market cap tops $200 billion, pushing regulators worldwide to focus on liquidity, transparency, and user protections.",
      },
      {
        country: "European Union",
        text: "Begins enforcing crypto rules, requiring exchanges to remove non-compliant stablecoins by March’s end, overseen by financial authorities.",
      },
      {
        country: "United Kingdom",
        text: "Consults on stablecoin custody, ensuring fiat-backed tokens have guaranteed redemption and strong reserve backing.",
      },
      {
        country: "Global",
        text: "Global securities body issues stablecoin guidelines, urging uniform reserve and disclosure standards for market stability.",
      },
    ],
  },
  {
    month: "January 2025",
    entries: [
      {
        country: "United States",
        text: "Executive order pushes for a national stablecoin framework by mid-2025, aiming to boost dollar-backed tokens globally.",
      },
      {
        country: "United States",
        text: "Appoints crypto-friendly regulators, shifting toward policies that support innovation and reduce heavy-handed enforcement.",
      },
      {
        country: "United States",
        text: "Bans central bank digital currencies, favoring private stablecoins, contrasting with Europe’s digital currency trials.",
      },
      {
        country: "United States",
        text: "Forms task force to explore a national crypto stockpile using seized assets, aiming to integrate digital assets into policy.",
      },
      {
        country: "European Union",
        text: "Fully rolls out crypto regulations, requiring stablecoin issuers to meet strict reserve, licensing, and transparency standards.",
      },
      {
        country: "United Kingdom",
        text: "Shapes stablecoin rules, focusing on fiat-backed tokens with clear reserve backing and user redemption rights.",
      },
      {
        country: "Global",
        text: "International body warns of stablecoin risks to financial stability, calling for coordinated global oversight to prevent mismanagement.",
      },
      {
        country: "Stablecoin Market",
        text: "Stablecoins gain traction in remittances, with firms using compliant tokens for cross-border payments, driven by new rules.",
      },
    ],
  },
  {
    month: "December 2024",
    entries: [
      {
        country: "United States",
        text: "Treasury finalizes rules classifying decentralized finance platforms as brokers, requiring tax reporting similar to traditional finance.",
      },
      {
        country: "United States",
        text: "Securities regulator pauses major crypto enforcement cases, hinting at a shift toward clearer rules under new leadership.",
      },
      {
        country: "European Union",
        text: "Crypto regulations take full effect, requiring stablecoin issuers to meet reserve and licensing rules, overseen by financial authorities.",
      },
      {
        country: "European Union",
        text: "Enforces transaction transparency rules for crypto, aligning with global standards to track cross-border transfers.",
      },
      {
        country: "United Kingdom",
        text: "Continues developing stablecoin rules, with plans for 2025 legislation to regulate fiat-backed tokens for payments.",
      },
      {
        country: "Global",
        text: "International regulators highlight tokenization’s impact, urging standards for stablecoin reserve transparency to ensure stability.",
      },
      {
        country: "Stablecoin Market",
        text: "Non-compliant stablecoins risk being removed from European exchanges, pushing issuers to align with new regulations.",
      },
    ],
  },
  {
    month: "November 2024",
    entries: [
      {
        country: "United States",
        text: "Crypto industry’s election influence secures a pro-crypto Congress, boosting prospects for stablecoin and crypto laws.",
      },
      {
        country: "United States",
        text: "Signals shift to less aggressive crypto enforcement, with plans to replace securities regulator with a pro-innovation leader.",
      },
      {
        country: "Singapore",
        text: "Supports tokenization trials, testing stablecoins and bonds in multiple currencies to advance digital finance.",
      },
      {
        country: "United Arab Emirates",
        text: "Tests cross-border digital currency transfers, complementing plans for stablecoin regulations to boost digital finance.",
      },
      {
        country: "European Union",
        text: "Prepares crypto transparency register, ensuring stablecoin issuers meet disclosure rules by year-end.",
      },
      {
        country: "Global",
        text: "Notes uneven global adoption of crypto transaction tracking rules, with leading regions setting compliance standards.",
      },
      {
        country: "Stablecoin Market",
        text: "Tokenized financial assets grow, with stablecoins increasingly integrated into traditional financial systems.",
      },
    ],
  },
  {
    month: "October 2024",
    entries: [
      {
        country: "United States",
        text: "Crypto firms challenge securities regulator’s authority, arguing overreach in classifying most crypto trades as securities.",
      },
      {
        country: "United States",
        text: "Major exchange pushes court to force new crypto rules, challenging the regulator’s enforcement-heavy approach.",
      },
      {
        country: "European Union",
        text: "Refines technical standards for stablecoin issuers, focusing on reserve audits and user protections for market stability.",
      },
      {
        country: "United Kingdom",
        text: "Enforces transaction tracking rules for crypto, aligning with global standards for transparency in digital asset transfers.",
      },
      {
        country: "Hong Kong",
        text: "Launches stablecoin testing program, allowing firms to trial tokenized payments for financial settlements.",
      },
      {
        country: "Global",
        text: "International report highlights stablecoin risks and benefits, urging unified standards for reserve management.",
      },
      {
        country: "Stablecoin Market",
        text: "Stablecoin issuers face growing scrutiny, with regulators emphasizing reserve transparency to prevent financial risks.",
      },
    ],
  },
  {
    month: "September 2024",
    entries: [
      {
        country: "United States",
        text: "Securities regulator rejects calls for new crypto rules, insisting existing laws apply, prompting industry legal challenges.",
      },
      {
        country: "United States",
        text: "Court ruling clarifies that certain crypto sales are not securities, influencing stablecoin regulatory debates.",
      },
      {
        country: "European Union",
        text: "Financial authorities coordinate crypto regulation rollout, ensuring consistent licensing for stablecoin issuers by December.",
      },
      {
        country: "United Kingdom",
        text: "Begins enforcing crypto transaction tracking, requiring platforms to collect data to align with global financial standards.",
      },
      {
        country: "Hong Kong",
        text: "Announces stablecoin testing program, inviting firms to explore tokenized money for banking settlements.",
      },
      {
        country: "Global",
        text: "Industry group releases standard for cross-blockchain transactions, aiding stablecoin and decentralized finance regulations.",
      },
      {
        country: "Stablecoin Market",
        text: "Stablecoin activity grows outside the U.S., pushing emerging markets to develop local regulatory frameworks.",
      },
    ],
  },
];

const FeaturredNarratives = () => {
  // State for search term
  const [searchTerm, setSearchTerm] = useState("");
  // State for selected country filter
  const [selectedCountry, setSelectedCountry] = useState("Country");
  // State for selected keyword filter (predefined for demo)
  const [selectedKeyword, setSelectedKeyword] = useState("All");
  // State for selected month filter
  const [selectedMonth, setSelectedMonth] = useState("Date");

  // Memoized list of all unique countries from the narratives data for the country filter dropdown
  const allCountries = useMemo(() => {
    const countries = new Set<string>();
    narrativesData.forEach((monthData) => {
      monthData.entries.forEach((entry) => {
        countries.add(entry.country);
      });
    });
    return ["Country", ...Array.from(countries).sort()];
  }, []);

  // Memoized list of all months from the narratives data for the month filter dropdown
  const allMonths = useMemo(() => {
    return ["Date", ...narrativesData.map((data) => data.month)];
  }, []);

  // Filtered narratives based on current search term and selected filters
  const filteredNarratives = useMemo(() => {
    return narrativesData
      .map((monthData) => {
        // Filter entries within each month
        const filteredEntries = monthData.entries.filter((entry) => {
          // Check if entry text or country matches the search term (case-insensitive)
          const matchesSearch = searchTerm
            ? entry.text.toLowerCase().includes(searchTerm.toLowerCase()) ||
              entry.country.toLowerCase().includes(searchTerm.toLowerCase())
            : true; // If no search term, all entries match

          // Check if entry country matches the selected country filter
          const matchesCountry =
            selectedCountry === "Country" || entry.country === selectedCountry;

          // Check if entry text includes the selected keyword (case-insensitive)
          const matchesKeyword =
            selectedKeyword === "All" ||
            entry.text.toLowerCase().includes(selectedKeyword.toLowerCase());

          // An entry matches if all criteria are met
          return matchesSearch && matchesCountry && matchesKeyword;
        });

        // Return the month data with its filtered entries
        return {
          ...monthData,
          entries: filteredEntries,
        };
      })
      // Filter out months that have no matching entries after filtering
      .filter((monthData) => {
        // Check if the month matches the selected month filter
        const matchesMonth =
          selectedMonth === "Date" || monthData.month === selectedMonth;
        // A month data block is included if its month matches and it has at least one matching entry
        return matchesMonth && monthData.entries.length > 0;
      });
  }, [searchTerm, selectedCountry, selectedKeyword, selectedMonth]); // Dependencies for memoization

  // Handler for search input changes
  const handleSearch = (value: string) => {
    setSearchTerm(value);
  };

  return (
    <section>
      {/* Header component */}
      <Header />

      <section className="center py-16">
        {/* Breadcrumb navigation */}
        <Breadcrumb className="pb-2">
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href="/">Home</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink href="/regulatory-tracker">Regulatory Tracker</BreadcrumbLink>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>

        {/* Page title */}
        <h1 className="text-3xl font-semibold mb-2">Regulatory Tracker</h1>

        {/* Page description */}
        {/* <p className="text-lg text-text-secondary text-justify mb-8">
          At MOFSE, we believe in transparency and establishing trust with our
          users. We bring you a curated selection of Regulatory Tracker that are
          making an impact in the crypto ecosystem. Whether it’s next-generation
          blockchain solutions, innovative DeFi platforms, or utility-driven
          tokens, we provide you with carefully selected featured narratives
          that are making real impact
        </p> */}

        {/* Search Bar and Filter Section - styled to match screenshot */}
        <div className="flex flex-col md:flex-row gap-4 mb-8">
          {/* Search Bar */}
          <div className="flex-1">
            <SearchBar
              handleSearch={handleSearch}
              placeholder="Search narratives..."
            />
          </div>

          {/* Filter dropdowns (Country, Keyword, Month) */}
          <div className="flex gap-4 flex-wrap">
            {/* Country Filter */}
            <Select
              onValueChange={setSelectedCountry}
              value={selectedCountry}
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Country" />
              </SelectTrigger>
              <SelectContent>
                {allCountries.map((country) => (
                  <SelectItem key={country} value={country}>
                    {country}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {/* Keyword Filter */}
            <Select
              onValueChange={setSelectedKeyword}
              value={selectedKeyword}
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Keyword" />
              </SelectTrigger>
              <SelectContent>
                {/* Predefined example keywords. In a real application, these might be dynamically generated or come from a backend. */}
                <SelectItem value="All">All Keywords</SelectItem>
                <SelectItem value="stablecoin">stablecoin</SelectItem>
                <SelectItem value="regulation">regulation</SelectItem>
                <SelectItem value="market">market</SelectItem>
                <SelectItem value="payments">payments</SelectItem>
                <SelectItem value="licensing">licensing</SelectItem>
                <SelectItem value="AML">AML</SelectItem>
              </SelectContent>
            </Select>

            {/* Month Filter */}
            <Select
              onValueChange={setSelectedMonth}
              value={selectedMonth}
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Month" />
              </SelectTrigger>
              <SelectContent>
                {allMonths.map((month) => (
                  <SelectItem key={month} value={month}>
                    {month}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Display Filtered Narratives */}
        <div>
          {filteredNarratives.length > 0 ? (
            filteredNarratives.map((monthData) => (
              <div key={monthData.month} className="mb-8">
                {/* Month heading */}
                <h2 className="text-xl font-semibold mb-4 text-text-primary">
                  {monthData.month}
                </h2>
                {/* List of entries for the month */}
                <ul className="list-disc pl-5">
                  {monthData.entries.map((entry, index) => (
                    <li key={index} className="mb-2 text-text-secondary">
                      <strong>{entry.country}:</strong> {entry.text}
                    </li>
                  ))}
                </ul>
              </div>
            ))
          ) : (
            // Message when no narratives are found
            <p className="text-text-secondary">No narratives found matching your criteria.</p>
          )}
        </div>
      </section>

      {/* Footer component */}
      <Footer />
    </section>
  );
};

export default FeaturredNarratives;