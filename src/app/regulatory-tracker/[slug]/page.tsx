import Footer from "@/components/comman/Footer";
import Header from "@/components/comman/Header";
import HTMLContentParser from "@/components/comman/HTMLContentParser";
import ShareButtons from "@/components/comman/ShareButtons";
import FeaturedNarrativeCard from "@/components/mofse-weekly/FeaturedNarrativeCard";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { api } from "@/lib/api";
import { formatDateTimeInYearMonthDay } from "@/lib/utils";
import { MoveUpRight } from "lucide-react";
import { Metadata } from "next";
import Link from "next/link";

export type paramsType = Promise<{ slug: string }>;

export async function generateMetadata({
  params,
}: {
  params: paramsType;
}): Promise<Metadata> {
  const { slug } = await params;
  const project = await api.projects.getById(slug);

  return {
    title: project.title,
    description: project.description,
    openGraph: {
      title: project.title,
      description: project.description,
      images: [{ url: project.projectIconUrl }],
      type: "article",
      publishedTime: project.updatedAt,
      authors: [project.description],
    },
    twitter: {
      card: "summary_large_image",
      title: project.title,
      description: project.description,
      images: [project.projectIconUrl],
    },
  };
}

export default async function Page({ params }: { params: paramsType }) {
  const { slug } = await params;
  const project = await api.projects.getById(slug);
  const relatedProjectsQuery = await api.projects.getRelated(project._id);
  const relatedProjects = relatedProjectsQuery.relatedProjects.slice(0, 4);

  return (
    <section>
      <Header />
      <section className="py-16">
        <div className="center">
          <Breadcrumb>
            <BreadcrumbList>
              <BreadcrumbItem>
                <BreadcrumbLink href="/regulatory-tracker">
                  Mofse Weekly
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbLink href={`/regulatory-tracker/${slug}`}>
                  {project.title}
                </BreadcrumbLink>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>

          <div className="flex flex-col md:flex-row gap-8 mt-8 mb-8">
            <div className="w-full md:w-1/2 lg:w-1/3">
              <picture>
                <img
                  src={project.projectIconUrl}
                  alt={project.title}
                  className="rounded-xl h-96  object-cover w-full"
                />
              </picture>
            </div>
            <div className="w-full md:w-1/2 lg:w-2/3">
              <h1 className="text-4xl font-semibold mb-8">{project.title}</h1>
              <p className="text-xl mb-8">By {project.description}</p>
              <div className="flex gap-8 mb-8">
                <span>{project.content.timeTook} Min Read</span>
                <span>
                  Last Updated On :{" "}
                  {formatDateTimeInYearMonthDay(project.updatedAt)}
                </span>
              </div>
              <ShareButtons
                slug={slug}
                title={project.title}
                path="featured-narratives"
              />
            </div>
          </div>
          <HTMLContentParser content={project.content.overview || ""} />
        </div>
      </section>
      <section className="py-16 bg-bg-dark-white">
        <div className="center">
          <div className="flex items-center justify-between mb-8">
            <span className="text-3xl font-semibold ">Other Mofse Weekly</span>
            <Link
              href="/regulatory-tracker"
              className="text-text-primary border border-text-primary px-4 py-2 rounded flex items-center gap-2"
            >
              <span>View All</span>
              <MoveUpRight className="h-4 w-4" />
            </Link>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {relatedProjects?.length === 0 && (
              <p className="text-lg text-text-secondary text-justify">
                Coming Soon
              </p>
            )}
            {relatedProjects?.map((project) => (
              <FeaturedNarrativeCard key={project._id} project={project} />
            ))}
          </div>
        </div>
      </section>
      <Footer />
    </section>
  );
}
