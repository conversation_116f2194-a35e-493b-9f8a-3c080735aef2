"use client";
import ChinaECNYChart from "./ChinaECNYChart";
import IndiaERsChart from "./IndiaERsChart";

interface CBDCCirculationChartsProps {
  country?: "china" | "india" | "both";
}

const CBDCCirculationCharts = ({ country = "both" }: CBDCCirculationChartsProps) => {
  if (country === "china") {
    return (
      <div className="w-full">
        <ChinaECNYChart />
      </div>
    );
  }

  if (country === "india") {
    return (
      <div className="w-full">
        <IndiaERsChart />
      </div>
    );
  }

  // Show both charts side by side
  return (
    <div className="w-full space-y-8">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <ChinaECNYChart />
        <IndiaERsChart />
      </div>
      
      {/* Additional data table */}
      <div className="bg-white p-6 rounded-lg shadow-sm">
        <h3 className="text-lg font-bold text-gray-900 mb-4">CBDC Data Summary</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* China Data Table */}
          <div>
            <h4 className="text-md font-semibold text-gray-800 mb-3 flex items-center">
              <span className="w-4 h-4 bg-red-400 rounded mr-2"></span>
              China eYuan
            </h4>
            <div className="overflow-x-auto">
              <table className="min-w-full text-sm">
                <thead>
                  <tr className="border-b border-gray-200">
                    <th className="text-left py-2 px-3 font-medium text-gray-700">Year</th>
                    <th className="text-right py-2 px-3 font-medium text-gray-700">Volume</th>
                  </tr>
                </thead>
                <tbody>
                  <tr className="border-b border-gray-100">
                    <td className="py-2 px-3 text-gray-600">2022</td>
                    <td className="py-2 px-3 text-right text-gray-900">0.1 Trillion</td>
                  </tr>
                  <tr className="border-b border-gray-100">
                    <td className="py-2 px-3 text-gray-600">2023</td>
                    <td className="py-2 px-3 text-right text-gray-900">1.8 Trillion</td>
                  </tr>
                  <tr>
                    <td className="py-2 px-3 text-gray-600">2024</td>
                    <td className="py-2 px-3 text-right text-gray-900">7 Trillion</td>
                  </tr>
                </tbody>
              </table>
            </div>
            <p className="text-xs text-blue-600 mt-2">Source: People&apos;s Bank of China</p>
          </div>

          {/* India Data Table */}
          <div>
            <h4 className="text-md font-semibold text-gray-800 mb-3 flex items-center">
              <span className="w-4 h-4 bg-cyan-400 rounded mr-2"></span>
              India eRs
            </h4>
            <div className="overflow-x-auto">
              <table className="min-w-full text-sm">
                <thead>
                  <tr className="border-b border-gray-200">
                    <th className="text-left py-2 px-3 font-medium text-gray-700">Year</th>
                    <th className="text-right py-2 px-3 font-medium text-gray-700">Circulation</th>
                  </tr>
                </thead>
                <tbody>
                  <tr className="border-b border-gray-100">
                    <td className="py-2 px-3 text-gray-600">2023</td>
                    <td className="py-2 px-3 text-right text-gray-900">********</td>
                  </tr>
                  <tr className="border-b border-gray-100">
                    <td className="py-2 px-3 text-gray-600">2024</td>
                    <td className="py-2 px-3 text-right text-gray-900">*********</td>
                  </tr>
                  <tr>
                    <td className="py-2 px-3 text-gray-600">2025</td>
                    <td className="py-2 px-3 text-right text-gray-900">**********</td>
                  </tr>
                </tbody>
              </table>
            </div>
            <p className="text-xs text-blue-600 mt-2">Source: Reserve Bank of India</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CBDCCirculationCharts;
