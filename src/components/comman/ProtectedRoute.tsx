"use client";

import { useProfile } from "@/lib/state";
import { usePathname, useRouter } from "next/navigation";
import { useEffect } from "react";
import { toast } from "sonner";
import Loader from "./Loader";

// Optional: A loading component to show while checking auth
const LoadingScreen = () => (
  <div className="flex items-center justify-center h-screen">
    <Loader/>
  </div>
);

const ProtectedRoute = ({ children }: { children: React.ReactNode }) => {
  const router = useRouter();
  const pathname = usePathname();
  const userQuery = useProfile();

  useEffect(() => {
    // We don't want to run the check until the initial fetch is complete.
    if (userQuery.isLoading) {
      return;
    }

    // If the fetch is complete and there is no user, redirect.
    if (!userQuery.data) {
      toast.error("Access denied. Please log in to view this page.");
      router.push(`/login?from=${encodeURIComponent(pathname)}`);
    }
  }, [userQuery.isLoading, userQuery.data, router, pathname]);

  // While the user's status is loading or if they are not authenticated,
  // show a loading screen to prevent a flash of protected content.
  if (userQuery.isLoading || !userQuery.data) {
    return <LoadingScreen />;
  }

  // If the user is authenticated, show the page content.
  return <>{children}</>;
};

export default ProtectedRoute;