import { FILTER_LIST } from "./constant";

/**
 * Calculates start and end dates based on a time range
 * @param timeRange - The time range to calculate dates for ('1m', '3m', '6m', '1y', 'all')
 * @returns An object containing startDate and endDate in YYYY-MM-DD format
 */
export function calculateDateRange(timeRange: string): { startDate: string; endDate:string } {
  const now = new Date();
  // Set endDate to today in YYYY-MM-DD format.
  const endDate = now.toISOString().split('T')[0];
  let startDate: Date;

  switch (timeRange) {
    case '24h':
      // 24 hours ago
      startDate = new Date(now);
      startDate.setDate(now.getDate() - 1);
      break;
    case '7d':
      // 7 days ago
      startDate = new Date(now);
      startDate.setDate(now.getDate() - 7);
      break;
    case '1m':
      // 1 month ago
      startDate = new Date(now);
      startDate.setMonth(now.getMonth() - 1);
      break;
    case '6m':
      // 6 months ago
      startDate = new Date(now);
      startDate.setMonth(now.getMonth() - 6);
      break;
    case '1y':
      // 1 year ago
      startDate = new Date(now);
      startDate.setFullYear(now.getFullYear() - 1);
      break;
    case '3y':
      // 3 years ago
      startDate = new Date(now);
      startDate.setFullYear(now.getFullYear() - 3);
      break;
    case '5y':
      // 5 years ago
      startDate = new Date(now);
      startDate.setFullYear(now.getFullYear() - 5);
      break;
    default:
      // Default to 7 days if something unexpected is passed.
      startDate = new Date(now);
      startDate.setDate(now.getDate() - 7);
  }
  
  // Return both dates in YYYY-MM-DD format.
  return { startDate: startDate.toISOString().split('T')[0], endDate };
}


const getTooltipText = (filter: string) => {
    if (filter === FILTER_LIST.TRANSACTION_VOLUME) {
      return "Volume";
    }
    if (filter === FILTER_LIST.ACTIVE_ADDRESS || filter === FILTER_LIST.SENDING_ADDRESS || filter === FILTER_LIST.RECEIVING_ADDRESS) {
      return "Address";
    }
    return "Count";
};



export function formatTooltip(value: string, filter: string) {
  // Ensure the input value is treated as a number.
  const numericValue = parseFloat(value);
  
  // Determine the tooltip text based on the filter.
  const toolTipText = getTooltipText(filter);

  // Check if the value is a valid number. If not, return a default or error state.
  if (isNaN(numericValue)) {
    return ["N/A", toolTipText];
  }

  // Check if the filter type is TRANSACTION_VOLUME.
  if ([FILTER_LIST.TRANSACTION_VOLUME, 'avg-transaction-value', 'total-fee'].includes(filter)) {
    // Round the value to the nearest whole number.
    const roundedValue = Math.round(numericValue);
    // Format the rounded value as a currency string.
    const formattedValue = `$${roundedValue.toLocaleString()}`;
    return [formattedValue, toolTipText];
  }

  // For any other filter type, format the original value without rounding or adding a currency symbol.
  const formattedValue = numericValue.toLocaleString();
  return [formattedValue, toolTipText];
};

export function formatChartDateWithoutYear(input: string): string {
  const date = new Date(input);

  const formatOptions: Intl.DateTimeFormatOptions = {
    day: "2-digit",
    month: "short",
  };

  return date.toLocaleDateString("en-GB", formatOptions);
}