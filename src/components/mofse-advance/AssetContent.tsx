"use client";

import React, { ComponentType } from "react";
// import TransactionCountChart from "@/components/mofse-advance/TransactionCountChart";
import { Coin } from "@/lib/api.interface";
import { FILTER_LIST } from "./constant";
import GenericChartWrapper from "./GenericChartWrapper";
import { StableCoinTable } from "../alerts/StableCoinsTable";

interface AssetContentProps {
  coin: Coin;
  filter: string;
}

function getHeading(filter: string) {
  switch (filter) {
    case FILTER_LIST.TRANSACTION_COUNT:
      return "Transaction Count";
    case FILTER_LIST.ACTIVE_ADDRESS:
      return "Active Address";
    case FILTER_LIST.SENDING_ADDRESS:
      return "Sending Address";
    case FILTER_LIST.RECEIVING_ADDRESS:
      return "Receiving Address";
    case FILTER_LIST.TRANSACTION_VOLUME:
      return "Transaction Volume";
    default:
      return "";
  }
}


const chartComponentMap: Record<string, ComponentType<any>> = {
  'coin-rankings': StableCoinTable
};

const AssetContent: React.FC<AssetContentProps> = ({ coin, filter }) => {

  const ChartToRender = chartComponentMap[filter];


  return (
    <div className="flex">
      <div className="flex-1 py-6 pl-6 overflow-x-auto">
        <div className="flex items-center gap-4 mb-6">
          <h2 className="text-lg font-medium text-white">
            {coin.symbol}: {getHeading(filter)}
          </h2>
        </div>
        <div className="space-y-6">
          {ChartToRender ? (
            <ChartToRender />
          ) : (
            <GenericChartWrapper assetType={coin.symbol} filter={filter} />
          )}
        </div>
      </div>
    </div>
  );
};

export default AssetContent;
