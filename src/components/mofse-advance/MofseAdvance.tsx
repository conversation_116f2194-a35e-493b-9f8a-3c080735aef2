"use client";
import AssetContent from "@/components/mofse-advance/AssetContent";
import { Tabs, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import Image from "next/image";
import React from "react";
import { Coin } from "@/lib/api.interface";
import {
  Wallet,
  ChevronDown,
  Minus
} from "lucide-react";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { useState } from "react";
import { cn } from "@/lib/utils";
import { FILTER_LIST } from "./constant";

const FILTERS = [
  {
    title: "On-Chain Metrics",
    icon: <Wallet />,
    options: [
      {
        label: "Sending Address",
        value: FILTER_LIST.SENDING_ADDRESS,
      },
      {
        label: "Receiving Address",
        value: FILTER_LIST.RECEIVING_ADDRESS,
      },
      {
        label: "Active Address",
        value: FILTER_LIST.ACTIVE_ADDRESS,
      },
       {
        label: "Transaction Count",
        value: FILTER_LIST.TRANSACTION_COUNT,
      },
      {
        label: "Transaction Volume",
        value: FILTER_LIST.TRANSACTION_VOLUME,
      }
    ],
  },
   {
    title: "Coins & Market Cap",
    icon: <Wallet />,
    options: [
      {
        label: "Coin Ranking",
        value: FILTER_LIST.COIN_RANKINGS,
      }
    ],
  }
];

const MofseAdvance = ({ coinList }: { coinList: Coin[] }) => {
  const [activeFilter, setActiveFilter] = useState<string>(
    FILTER_LIST.SENDING_ADDRESS
  );
  const [selectedCoin, setSelectedCoin] = useState<string>(coinList[0].symbol);

  // const isBitcoin = selectedCoin === "BTC";
  // const isEthereum = selectedCoin === "ETH";
  const isUSDT = selectedCoin === "USDT";
  const isUSDC = selectedCoin === "USDC";

  return (
    <div className="py-0 text-white">
      <Tabs
        defaultValue={selectedCoin}
        className="w-full overflow-x-auto"
        onValueChange={setSelectedCoin}
      >
        <div className="border-b border-t border-gray-600" style={{ backgroundColor: '#222831' }}>
          <TabsList className="flex space-x-4 overflow-x-auto whitespace-nowrap rounded-none px-2 h-13 p-0" style={{ backgroundColor: '#222831' }}>
            {coinList.map((coin) => (
              <TabsTrigger
                key={coin.symbol}
                value={coin.symbol}
                className="flex items-center space-x-1 rounded-none border-transparent px-3 py-6 text-sm font-medium border-0 text-gray-300 data-[state=active]:border-b-2 data-[state=active]:border-bg-primary data-[state=active]:text-white data-[state=active]:shadow-none hover:text-bg-primary transition-colors"
                style={{ backgroundColor: 'transparent' }}
              >
                <Image
                  src={coin.iconUrl}
                  alt={coin.name}
                  width={24}
                  height={24}
                  className="mr-1"
                />
                <span className="text-base font-medium">{coin.symbol}</span>
              </TabsTrigger>
            ))}
          </TabsList>
        </div>
      </Tabs>
      <div className="flex tablet:flex-row flex-col">
        <div className="w-full tablet:w-72 tablet:h-screen tablet:overflow-y-auto tablet:sticky tablet:top-0 tablet:border-r border-gray-600 py-4 pr-4 ">
          {/* <p className="text-lg font-semibold mb-4 mt-8">On-Chain Metrics</p> */}
          {FILTERS.length > 0 && (
            <Accordion type="multiple" className="w-full"
            defaultValue={FILTERS.map(filter => filter.title)}
            > {/* Use "multiple" if you want more than one open at a time */}
              {FILTERS.map((filter) => (
                <AccordionItem value={filter.title} key={filter.title} className="border-[#222831] border-2">
                  <AccordionTrigger className="group flex items-center justify-between w-full py-3 pl-2 pr-4 text-gray-900 text-base font-medium hover:bg-gray-700 data-[state=open]:bg-gray-100 data-[state=closed]:bg-gray-100 hover:no-underline [&>svg]:w-5 [&>svg]:h-5 [&>svg]:shrink-0 [&>svg]:text-gray-400 [&>svg]:transition-transform [&>svg]:duration-200 [&[data-state=open]>svg]:rotate-90 border ">
                    <span className="flex items-center justify-between gap-3 w-full cursor-pointer"> {/* Wrapper for icon and title */}
                      <div className="flex items-center gap-3">
                      {filter.icon}
                      {filter.title}
                      </div>
                      <ChevronDown/>
                    </span>
                  </AccordionTrigger>
                  <AccordionContent className="pb-1 pt-1">
                    <ul className="space-y-1 ml-2">
                      {filter.options.map((option) => (
                        <li
                          className={cn(
                            "flex items-center gap-2 cursor-pointer px-4 rounded",
                            "text-gray-300 hover:bg-bg-primary hover:text-black",
                            activeFilter === option.value
                              ? "bg-bg-primary text-black"
                              : ""
                          )}
                          onClick={() => setActiveFilter(option.value)}
                          key={option.value}
                        >
                          <Minus className="mr-2" /> {option.label}
                        </li>
                      ))}
                    </ul>
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          )}
        </div>
        <div className="w-full tablet:flex-1 tablet:min-w-0">
          {/* {isBitcoin && (
            <AssetContent
              coin={coinList.find((coin) => coin.symbol === "BTC")!}
              filter={activeFilter}
            />
          )}
          {isEthereum && (
            <AssetContent
              coin={coinList.find((coin) => coin.symbol === "ETH")!}
              filter={activeFilter}
            />
          )} */}
          {isUSDT && (
            <AssetContent
              coin={coinList.find((coin) => coin.symbol === "USDT")!}
              filter={activeFilter}
            />
          )}
          {isUSDC && (
            <AssetContent
              coin={coinList.find((coin) => coin.symbol === "USDC")!}
              filter={activeFilter}
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default MofseAdvance;