"use client";
import React from "react";
import { AssetValueChange } from "./AssetTable";
import Marque<PERSON> from "react-fast-marquee";
import { formatPrice } from "@/lib/utils";
import { useCoins } from "@/lib/state";

const AssetMarque = () => {
  // Use the same React Query hook as AssetTable with matching parameters
  const coinsQuery = useCoins({
    orderBy: "marketCap",
    offset: 0,
    limit: 10,
    order: "DESC",
    searchText: "",
    isStableCoinsEnabled: false, // Show cryptocurrencies in marquee
  });

  const allCoins = coinsQuery.data?.data.coins || [];

  // Show loading state or empty marquee if no data
  if (coinsQuery.isLoading || allCoins.length === 0) {
    return (
      <section className="py-2 bg-[#1d1d1d]">
        <div className="text-center text-gray-50 py-4">
          {coinsQuery.isLoading ? "Loading..." : "No data available"}
        </div>
      </section>
    );
  }

  return (
    <section className="py-2 bg-[#1d1d1d]">
      <Marquee speed={50} gradient={false} autoFill={true}>
        {allCoins.map((coin) => (
          <div key={coin.uuid} className="flex items-center gap-4 mr-4">
            <picture>
              <img src={coin.iconUrl} alt={coin.name} className="w-6 h-6" />
            </picture>
            <div>
              <p className="text-sm text-gray-50">{coin.name}</p>
              <div className="flex items-center gap-2">
                <span className="text-xs font-medium text-gray-50">
                  ${formatPrice(parseFloat(coin.price))}
                </span>
                <AssetValueChange value={coin.change_1h} />
              </div>
            </div>
          </div>
        ))}
      </Marquee>
    </section>
  );
};

export default AssetMarque;
