import Link from "next/link";
import { AssetTable } from "./AssetTable";

export async function AssetSection() {
  return (
    <section id="assetTable">
      <div className="center pb-16 pt-8">
        <p className="mb-4">
          Below, you can explore thousands of digital assets, including cryptocurrencies and stablecoins, tracked by market capitalization and trading volume. Please note that the data presented reflects only trading volume on tracked exchanges. For on-chain (blockchain) transactional volume, refer to the {" "}
          <span>
            <Link href={"/cryptocurrency"} className="underline">
              Cryptocurrency
            </Link>
          </span>{" "}
          section.
        </p>
        <AssetTable />
      </div>
    </section>
  );
}
